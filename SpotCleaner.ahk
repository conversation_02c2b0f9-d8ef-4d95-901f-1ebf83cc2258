#Requires AutoHotkey v1.1.33+ ; use only autohotkey version 1.1 syntax
#SingleInstance Force
#Persistent
#NoEnv
SetBatchLines, -1
SetWorkingDir %A_ScriptDir%

; === CONFIGURATION ===
watchIntervalMin := 5
defaultMaxFileAgeHours := 20  ; Default age for new folders
baseDir := "G:\audio\Spots\Sync"
configIni := baseDir . "\config.ini"
logFile := baseDir . "\deletion.log"

; === Initialize config.ini if it doesn't exist ===
InitializeConfig()

SetTimer, WatchFolders, % watchIntervalMin * 60000

WatchFolders:
FormatTime, today, , yyyyMMdd
Loop, Files, %baseDir%\*, D
{
    folderName := A_LoopFileName
    fullPath := A_LoopFileFullPath

    ; === Skip folder if not in config.ini (manual management only) ===
    IniRead, folderMaxAge, %configIni%, %folderName%, maxFileAgeHours, NOTFOUND
    if (folderMaxAge = "NOTFOUND") {
        continue
    }

    ; === Check and delete files older than folder's maxFileAgeHours ===
    deletedAny := false
    Loop, Files, %fullPath%\*.mp3
    {
        ; Get file modification time
        FileGetTime, fileTime, %A_LoopFileFullPath%, M

        ; Calculate file age in hours
        EnvSub, fileAge, A_Now, %fileTime%, Hours

        ; Delete if file is older than this folder's maxFileAgeHours
        if (fileAge >= folderMaxAge)
        {
            FileDelete, %A_LoopFileFullPath%
            deletedAny := true

            ; === Log action ===
            FormatTime, ts, , yyyy-MM-dd HH:mm:ss
            FileAppend, %ts% | Deleted: %A_LoopFileFullPath% | Folder: %folderName% | Age: %fileAge% hours | Threshold: %folderMaxAge% hours`n, %logFile%
        }
    }

    ; Update last processed date if any files were deleted
    if (deletedAny)
        IniWrite, %today%, %configIni%, %folderName%, last_deleted
}
return

; === Initialize config.ini with all folders if file doesn't exist ===
InitializeConfig()
{
    global baseDir, configIni
    
    ; Check if config.ini already exists
    IfExist, %configIni%
        return
    ; Create config.ini with all existing folders
    Loop, Files, %baseDir%\*, D
    {
        folderName := A_LoopFileName

        ; Write folder section to config.ini
        IniWrite, %defaultMaxFileAgeHours%, %configIni%, %folderName%, maxFileAgeHours
        IniWrite, %A_Space%, %configIni%, %folderName%, last_deleted
    }
}
