#Requires AutoHotkey v1.1.33+ ; use only autohotkey version 1.1 syntax
#SingleInstance Force
#Persistent
#NoEnv
SetBatchLines, -1
SetWorkingDir %A_ScriptDir%

; === CONFIGURATION ===
watchIntervalMin := 5
windowMinutes := 60
baseDir := "G:\audio\Spots\Sync"
configIni := A_ScriptDir . "\config.ini"
logFile := A_ScriptDir . "\deletion.log"

; === Initialize config.ini if it doesn't exist ===
InitializeConfig()

SetTimer, WatchFolders, % watchIntervalMin * 60000

WatchFolders:
FormatTime, today, , yyyyMMdd
Loop, Files, %baseDir%\*, D
{
    folderName := A_LoopFileName
    fullPath := A_LoopFileFullPath

    ; === Skip folder if not in config.ini (manual management only) ===
    IniRead, delTimeStr, %configIni%, %folderName%, deletetime, NOTFOUND
    if (delTimeStr = "NOTFOUND") {
        continue
    }

    ; === Skip if already deleted today ===
    IniRead, lastDeleted, %configIni%, %folderName%, last_deleted, 
    if (lastDeleted = today)
        continue

    ; === Parse deletetime (already read above) ===
    if (StrLen(delTimeStr) = 3)
        delTimeStr := "0" . delTimeStr

    timeHH := SubStr(delTimeStr, 1, 2) + 0
    timeMM := SubStr(delTimeStr, 3, 2) + 0

    delMins := timeHH * 60 + timeMM

    ; === Current time in minutes ===
    FormatTime, nowHH,, HH
    FormatTime, nowMM,, mm
    nowMins := nowHH * 60 + nowMM

    ; === Check if deletion should occur ===
    shouldDelete := false
    deleteReason := ""

    ; Normal deletion window (within 60 minutes after scheduled time)
    if (nowMins >= delMins && nowMins < delMins + windowMinutes)
    {
        shouldDelete := true
        deleteReason := "Normal window"
    }
    ; Failsafe: If more than 2 hours past deletion time and not deleted today
    else if (nowMins >= delMins + 120)  ; 120 minutes = 2 hours
    {
        shouldDelete := true
        deleteReason := "Failsafe (>2hrs late)"
    }
    ; Handle next-day scenario (deletion time was late at night)
    else if (delMins > nowMins && (delMins - nowMins) > 720)  ; More than 12 hours difference suggests next day
    {
        ; Check if we're past the deletion time considering it was yesterday
        nextDayDelMins := delMins - 1440  ; Convert to negative for yesterday
        if (nowMins >= nextDayDelMins + 120)  ; More than 2 hours past yesterday's deletion time
        {
            shouldDelete := true
            deleteReason := "Failsafe (next day, >2hrs late)"
        }
    }

    if (shouldDelete)
    {
        deletedAny := false
        Loop, Files, %fullPath%\*.mp3
        {
            FileDelete, %A_LoopFileFullPath%
            deletedAny := true

            ; === Log action ===
            FormatTime, ts, , yyyy-MM-dd HH:mm:ss
            FileAppend, %ts% | Deleted: %A_LoopFileFullPath% | Folder: %folderName% | Reason: %deleteReason%`n, %logFile%
        }

        if (deletedAny)
            IniWrite, %today%, %configIni%, %folderName%, last_deleted
    }
}
return

; === Initialize config.ini with all folders if file doesn't exist ===
InitializeConfig()
{
    global baseDir, configIni
    
    ; Check if config.ini already exists
    IfExist, %configIni%
        return
    ; Create config.ini with all existing folders
    Loop, Files, %baseDir%\*, D
    {
        folderName := A_LoopFileName

        ; Extract time string from folder name and calculate default delete time
        defaultDeleteTime := "0100"  ; Default fallback time (01:00 in 24-hour format)

        if RegExMatch(folderName, "\b(\d{3,4})", match)
        {
            timeStr := match1
            if (StrLen(timeStr) = 3)
                timeStr := "0" . timeStr

            hour := SubStr(timeStr, 1, 2) + 0
            min := SubStr(timeStr, 3, 2) + 0

            ; Add 1 hour = default delete time
            totalMins := hour * 60 + min + 60
            if (totalMins >= 1440)
                totalMins -= 1440

            defHour := Floor(totalMins / 60)
            defMin := Mod(totalMins, 60)

            ; Format as HHMM in 24-hour format
            defHourStr := (defHour < 10) ? "0" . defHour : defHour
            defMinStr := (defMin < 10) ? "0" . defMin : defMin

            defaultDeleteTime := defHourStr . defMinStr
        }

        ; Write folder section to config.ini
        IniWrite, %defaultDeleteTime%, %configIni%, %folderName%, deletetime
        IniWrite, %A_Space%, %configIni%, %folderName%, last_deleted
    }
}
