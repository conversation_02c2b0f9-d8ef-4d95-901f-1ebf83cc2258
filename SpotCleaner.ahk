#Requires AutoHotkey v1.1.33+ ; use only autohotkey version 1.1 syntax
#SingleInstance Force
#Persistent
#NoEnv
SetBatchLines, -1
SetWorkingDir %A_ScriptDir%

; === CONFIGURATION ===
watchIntervalMin := 5
maxFileAgeHours := 20  ; Delete files older than 20 hours
baseDir := "G:\audio\Spots\Sync"
configIni := A_ScriptDir . "\config.ini"
logFile := A_ScriptDir . "\deletion.log"

; === Initialize config.ini if it doesn't exist ===
InitializeConfig()

SetTimer, WatchFolders, % watchIntervalMin * 60000

WatchFolders:
FormatTime, today, , yyyyMMdd
Loop, Files, %baseDir%\*, D
{
    folderName := A_LoopFileName
    fullPath := A_LoopFileFullPath

    ; === Skip folder if not in config.ini (manual management only) ===
    IniRead, delTimeStr, %configIni%, %folderName%, deletetime, NOTFOUND
    if (delTimeStr = "NOTFOUND") {
        continue
    }

    ; === Check and delete files older than maxFileAgeHours ===
    deletedAny := false
    Loop, Files, %fullPath%\*.mp3
    {
        ; Get file modification time
        FileGetTime, fileTime, %A_LoopFileFullPath%, M

        ; Calculate file age in hours
        EnvSub, fileAge, A_Now, %fileTime%, Hours

        ; Delete if file is older than maxFileAgeHours
        if (fileAge >= maxFileAgeHours)
        {
            FileDelete, %A_LoopFileFullPath%
            deletedAny := true

            ; === Log action ===
            FormatTime, ts, , yyyy-MM-dd HH:mm:ss
            FileAppend, %ts% | Deleted: %A_LoopFileFullPath% | Folder: %folderName% | Age: %fileAge% hours`n, %logFile%
        }
    }

    ; Update last processed date if any files were deleted
    if (deletedAny)
        IniWrite, %today%, %configIni%, %folderName%, last_deleted
}
return

; === Initialize config.ini with all folders if file doesn't exist ===
InitializeConfig()
{
    global baseDir, configIni
    
    ; Check if config.ini already exists
    IfExist, %configIni%
        return
    ; Create config.ini with all existing folders
    Loop, Files, %baseDir%\*, D
    {
        folderName := A_LoopFileName

        ; Set placeholder delete time (not used in file-age-based deletion)
        defaultDeleteTime := "0000"  ; Placeholder - actual deletion based on file age

        ; Write folder section to config.ini
        IniWrite, %defaultDeleteTime%, %configIni%, %folderName%, deletetime
        IniWrite, %A_Space%, %configIni%, %folderName%, last_deleted
    }
}
