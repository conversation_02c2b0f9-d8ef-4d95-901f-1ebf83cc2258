#Requires AutoHotkey v1.1.33+ ; use only autohotkey version 1.1 syntax
#SingleInstance Force
#Persistent
#NoEnv
SetBatchLines, -1
SetWorkingDir %A_ScriptDir%

; === CONFIGURATION ===
watchIntervalMin := 5
windowMinutes := 60
baseDir := "G:\audio\Spots\Sync"
configIni := baseDir . "\config.ini"
logFile := baseDir . "\deletion.log"

; === Initialize config.ini if it doesn't exist ===
InitializeConfig()

SetTimer, WatchFolders, % watchIntervalMin * 60000

WatchFolders:
FormatTime, today, , yyyyMMdd
Loop, Files, %baseDir%\*, D
{
    folderName := A_LoopFileName
    fullPath := A_LoopFileFullPath

    ; === Skip folder if not in config.ini (manual management only) ===
    IniRead, delTimeStr, %configIni%, %folderName%, deletetime, NOTFOUND
    if (delTimeStr = "NOTFOUND") {
        continue
    }

    ; === Skip if already deleted today ===
    IniRead, lastDeleted, %configIni%, %folderName%, last_deleted, 
    if (lastDeleted = today)
        continue

    ; === Parse deletetime (already read above) ===
    if (StrLen(delTimeStr) = 6)
        delTimeStr := "0" . delTimeStr

    timeHH := SubStr(delTimeStr, 1, 2) + 0
    timeMM := SubStr(delTimeStr, 3, 2) + 0
    ampm := SubStr(delTimeStr, 5)

    if (ampm = "pm" && timeHH < 12)
        timeHH += 12
    else if (ampm = "am" && timeHH = 12)
        timeHH := 0

    delMins := timeHH * 60 + timeMM

    ; === Current time in minutes ===
    FormatTime, nowHH,, HH
    FormatTime, nowMM,, mm
    nowMins := nowHH * 60 + nowMM

    ; === Within deletion window? ===
    if (nowMins >= delMins && nowMins < delMins + windowMinutes)
    {
        deletedAny := false
        Loop, Files, %fullPath%\*.mp3
        {
            FileDelete, %A_LoopFileFullPath%
            deletedAny := true

            ; === Log action ===
            FormatTime, ts, , yyyy-MM-dd HH:mm:ss
            FileAppend, %ts% | Deleted: %A_LoopFileFullPath% | Folder: %folderName%`n, %logFile%
        }

        if (deletedAny)
            IniWrite, %today%, %configIni%, %folderName%, last_deleted
    }
}
return

; === Initialize config.ini with all folders if file doesn't exist ===
InitializeConfig()
{
    Global configIni
	; Check if config.ini already exists
    IfExist, %configIni%
        return
	; Create config.ini with all existing folders
    Loop, Files, %baseDir%\*, D
    {
        folderName := A_LoopFileName

        ; Extract time string from folder name and calculate default delete time
        defaultDeleteTime := "0100am"  ; Default fallback time (1:00 AM)

        if RegExMatch(folderName, "\b(\d{3,4}(am|pm))\b", match)
        {
            timeStr := match1
            if (StrLen(timeStr) = 5)
                timeStr := "0" . timeStr

            hour := SubStr(timeStr, 1, 2) + 0
            min := SubStr(timeStr, 3, 2) + 0
            ampm := SubStr(timeStr, 5)

            ; Convert to 24-hour time
            if (ampm = "pm" && hour < 12)
                hour += 12
            else if (ampm = "am" && hour = 12)
                hour := 0

            ; Add 1 hour = default delete time
            totalMins := hour * 60 + min + 60
            if (totalMins >= 1440)
                totalMins -= 1440

            defHour := Floor(totalMins / 60)
            defMin := Mod(totalMins, 60)

            ; Convert back to 12-hour format with am/pm
            outAmpm := (defHour >= 12) ? "pm" : "am"
            outHour := Mod(defHour, 12)
            if (outHour = 0)
                outHour := 12
            outMinStr := (defMin < 10) ? "0" . defMin : defMin
            outHourStr := (outHour < 10) ? "0" . outHour : outHour

            defaultDeleteTime := outHourStr . outMinStr . outAmpm
        }

        ; Write folder section to config.ini
        IniWrite, %defaultDeleteTime%, %configIni%, %folderName%, deletetime
        IniWrite, %A_Space%, %configIni%, %folderName%, last_deleted
    }
}
